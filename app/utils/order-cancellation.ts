// 🦠 BACTERIAL ORDER CANCELLATION UTILITIES
// Small, pure, self-contained order cancellation configuration functions

import type { FormFieldConfig } from './form';
import { validateEnum } from './validation';

/**
 * Valid Shopify order cancellation reasons.
 * Bacterial approach: centralized configuration that can be "yoinked".
 */
export const CANCELLATION_REASONS = [
  'customer',
  'declined', 
  'fraud',
  'inventory',
  'other',
  'staff'
] as const;

/**
 * Order cancellation configuration interface.
 */
export interface CancelHighRiskOrderConfig {
  cancellation_reason_to_set: string;
  ignore_unpaid_orders: boolean;
  refund_payment_for_cancelled_orders: boolean;
  restock_inventory_for_cancelled_orders: boolean;
  email_customer_when_cancelling: boolean;
  staff_note_for_timeline: string;
  add_this_order_tag_when_cancelling: string;
}

/**
 * Default order cancellation configuration.
 * Usage: const config = { ...DEFAULT_CANCELLATION_CONFIG, cancellation_reason_to_set: 'fraud' }
 */
export const DEFAULT_CANCELLATION_CONFIG: CancelHighRiskOrderConfig = {
  cancellation_reason_to_set: 'fraud',
  ignore_unpaid_orders: true,
  refund_payment_for_cancelled_orders: false,
  restock_inventory_for_cancelled_orders: true,
  email_customer_when_cancelling: false,
  staff_note_for_timeline: '',
  add_this_order_tag_when_cancelling: ''
};

/**
 * Validate cancellation reason.
 * Usage: validateCancellationReason('fraud') → null (valid)
 */
export const validateCancellationReason = (reason: string): string | null => {
  const validReason = validateEnum(reason, CANCELLATION_REASONS);
  if (!validReason) {
    return `Cancellation reason must be one of: ${CANCELLATION_REASONS.join(', ')}`;
  }
  return null;
};

/**
 * Form field configurations for order cancellation.
 * Bacterial approach: centralized field definitions.
 */
export const CANCELLATION_FORM_FIELDS: FormFieldConfig[] = [
  {
    key: 'cancellation_reason_to_set',
    type: 'text',
    label: 'Cancellation reason to set',
    helpText: `Must be one of: ${CANCELLATION_REASONS.join(', ')}`,
    required: true,
    validator: validateCancellationReason
  },
  {
    key: 'staff_note_for_timeline',
    type: 'text',
    label: 'Staff note for timeline',
    helpText: 'Optional note to add to the order timeline'
  },
  {
    key: 'add_this_order_tag_when_cancelling',
    type: 'text',
    label: 'Add this order tag when cancelling',
    helpText: 'Optional tag to add to cancelled orders'
  },
  {
    key: 'ignore_unpaid_orders',
    type: 'checkbox',
    label: 'Ignore unpaid orders',
    helpText: 'Skip cancellation for orders that have not been paid'
  },
  {
    key: 'refund_payment_for_cancelled_orders',
    type: 'checkbox',
    label: 'Refund payment for cancelled orders',
    helpText: 'Automatically refund payments when cancelling orders'
  },
  {
    key: 'restock_inventory_for_cancelled_orders',
    type: 'checkbox',
    label: 'Restock inventory for cancelled orders',
    helpText: 'Return items to inventory when cancelling orders'
  },
  {
    key: 'email_customer_when_cancelling',
    type: 'checkbox',
    label: 'Email customer when cancelling',
    helpText: 'Send cancellation notification email to customer'
  }
];

/**
 * Get text fields from cancellation form configuration.
 * Usage: getTextFields() → FormFieldConfig[]
 */
export const getTextFields = (): FormFieldConfig[] => {
  return CANCELLATION_FORM_FIELDS.filter(field => field.type === 'text');
};

/**
 * Get checkbox fields from cancellation form configuration.
 * Usage: getCheckboxFields() → FormFieldConfig[]
 */
export const getCheckboxFields = (): FormFieldConfig[] => {
  return CANCELLATION_FORM_FIELDS.filter(field => field.type === 'checkbox');
};

/**
 * Validate complete cancellation configuration.
 * Usage: validateCancellationConfig(config) → { valid: boolean, errors: Record<string, string> }
 */
export const validateCancellationConfig = (
  config: CancelHighRiskOrderConfig
): { valid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};
  
  // Validate cancellation reason
  const reasonError = validateCancellationReason(config.cancellation_reason_to_set);
  if (reasonError) {
    errors.cancellation_reason_to_set = reasonError;
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Check if configuration has any validation errors.
 * Usage: hasValidationErrors(config) → boolean
 */
export const hasValidationErrors = (config: CancelHighRiskOrderConfig): boolean => {
  const validation = validateCancellationConfig(config);
  return !validation.valid;
};

/**
 * Get cancellation reason options for select components.
 * Usage: getCancellationReasonOptions() → { label: string, value: string }[]
 */
export const getCancellationReasonOptions = () => {
  return CANCELLATION_REASONS.map(reason => ({
    label: reason.charAt(0).toUpperCase() + reason.slice(1),
    value: reason
  }));
};
