import { JobType } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import prisma from "../db.server";
import { logToFile } from "../utils/logger.server";
import {
  extractUserErrors,
  extractOperationResult,
  createBulkOperationMutation,
  parseGraphQLResponse
} from "../utils/graphql";
import { createSuccess, createError, type Result } from "../utils/result";
import { getBulkQuery, supportsBulkOperation } from "../utils/bulk-queries";

// Bulk queries moved to bacterial utility: app/utils/bulk-queries.ts

/**
 * Initiates a Shopify bulk operation for a given automation.
 * Now uses bacterial utilities for cleaner, more testable code.
 *
 * @param admin The Shopify Admin API client.
 * @param automation The automation record from the database.
 * @returns A promise that resolves with a standardized Result object.
 */
export async function startBulkOperation(
  admin: ShopifyUnAuthenticatedAdminClient,
  automation: { id: number; type: JobType; shop: string; }
): Promise<Result<{ bulkOperationId: string }>> {

  // Use bacterial utility to check if automation supports bulk operations
  if (!supportsBulkOperation(automation.type)) {
    return createError("This automation does not support manual bulk runs.");
  }

  const bulkQuery = getBulkQuery(automation.type)!; // Safe because we checked above

  try {
    // Execute GraphQL mutation using bacterial utility
    const response = await admin.graphql(
      createBulkOperationMutation(),
      { variables: { query: bulkQuery } }
    );

    // Parse response using bacterial utility
    const responseJson = await parseGraphQLResponse(response);
    logToFile("bulk-operations", "info", JSON.stringify(responseJson));

    // Extract user errors using bacterial utility
    const userErrors = extractUserErrors(responseJson, 'bulkOperationRunQuery');
    if (userErrors) {
      const errorMessage = `Failed to start bulk operation: ${userErrors[0]}`;
      logToFile(automation.id.toString(), "error", JSON.stringify(userErrors));
      return createError(errorMessage, userErrors);
    }

    // Extract bulk operation result using bacterial utility
    const bulkOperation = extractOperationResult(responseJson, 'bulkOperationRunQuery', 'bulkOperation');
    if (!bulkOperation?.id) {
      return createError("No bulk operation ID returned from Shopify");
    }

    // Create tracker job (this remains eukaryotic - application-specific orchestration)
    await prisma.job.create({
      data: {
        shop: automation.shop,
        type: automation.type,
        data: JSON.stringify({ bulkOperationId: bulkOperation.id, automationId: automation.id }),
        status: "processing",
      }
    });

    logToFile(automation.id.toString(), "info", `Successfully started bulk operation ${bulkOperation.id}`);
    return createSuccess("Bulk run started successfully.", { bulkOperationId: bulkOperation.id });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(automation.id.toString(), "error", `Error starting bulk run: ${errorMessage}`);
    return createError("An internal error occurred while starting the bulk run.");
  }
}