import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useFetcher, useLoaderData, useSearchParams, useSubmit, Link as RemixLink } from "@remix-run/react";
import { Button, Card, Grid, Layout, Page, Text, TextField, Tabs, BlockStack, InlineStack } from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { useEffect, useMemo } from "react";
import type { JobType } from "@prisma/client";
import { TASKS_LIBRARY } from "../data/tasks";
import { NumberedPagination } from "../components/NumberedPagination";
import {
  extractSearchParams,
  createSearchFormData,
  calculatePagination
} from "../utils/search";
import {
  createTaskTemplateFindOptions,
  createTaskTemplateCountOptions,
  transformDbTasksToDisplay,
  extractUniqueValues,
  needsSeeding,
  createAutomationLookupSet,
  isTaskInstalled
} from "../utils/database-query";
import {
  LIBRARY_TABS,
  createTabState,
  createTab<PERSON>hangeHand<PERSON>
} from "../utils/tabs";

// Define the shape of a task as it's used in the component and returned by the loader
type DisplayTask = {
  id: number;
  title: string;
  description: string;
  category: string;
  type: JobType;
  trigger: string;
};

// Define the overall shape of the data returned by the loader
type LoaderData = {
  tasks: DisplayTask[];
  installedTypes: JobType[];
  needsSeeding: boolean;
  currentPage: number;
  totalPages: number;
};

// --- REMIX LOADER ---
// Fetches available tasks and checks which ones are already installed.
export const loader = async ({ request }: LoaderFunctionArgs): Promise<Response> => {
    const { session } = await authenticate.admin(request);
    const url = new URL(request.url);

    // Use bacterial utility to extract search parameters
    const searchConfig = extractSearchParams(url);

    // Check if seeding is needed using bacterial utility
    const taskTemplateCount = await prisma.taskTemplate.count();
    const seedingNeeded = needsSeeding(TASKS_LIBRARY.length, taskTemplateCount);

    // Use bacterial utilities to build database queries
    const findOptions = createTaskTemplateFindOptions(searchConfig);
    const countOptions = createTaskTemplateCountOptions(searchConfig);

    // Fetch task templates from the database with pagination
    const [availableTasksFromDB, totalTasks] = await Promise.all([
      prisma.taskTemplate.findMany(findOptions),
      prisma.taskTemplate.count(countOptions)
    ]);

    // Use bacterial utility to calculate pagination
    const pagination = calculatePagination(totalTasks, searchConfig.pageSize!, searchConfig.page!);

    // Use bacterial utility to transform database tasks to display tasks
    const availableTasks: DisplayTask[] = transformDbTasksToDisplay(availableTasksFromDB);

    // Get the types of automations the user has already installed
    const installedAutomations = await prisma.automation.findMany({
        where: { shop: session.shop },
        select: { type: true },
    });

    // Use bacterial utility to extract unique values
    const installedTypes = extractUniqueValues(installedAutomations, 'type');

    const data: LoaderData = {
      tasks: availableTasks,
      installedTypes,
      needsSeeding: seedingNeeded,
      currentPage: searchConfig.page!,
      totalPages: pagination.totalPages
    };
    return Response.json(data);
};

export const action = async ({ request }: ActionFunctionArgs) => {
    const formData = await request.formData();
    const { _action } = Object.fromEntries(formData);

    if (_action === "seedTaskTemplates") {
        for (const task of TASKS_LIBRARY) {
            // Destructure to separate model fields from extra fields like optionsSchema
            const { optionsSchema, ...taskTemplateData } = task;

            await prisma.taskTemplate.upsert({
                // Use the 'type' field as the unique identifier for the lookup.
                // This requires a @unique constraint on the 'type' field in your schema.
                where: { type: task.type }, 
                
                // If a record with this type is found, update it.
                update: {
                    ...taskTemplateData,
                },
                // If no record is found, create a new one.
                create: {
                    ...taskTemplateData,
                },
            });
        }
        return Response.json({ ok: true, message: "Task templates seeded." });
    }
    return Response.json({ ok: false, error: "Invalid action" }, { status: 400 });
};

export default function TaskLibrary() {
  const { tasks, installedTypes, needsSeeding, currentPage, totalPages } = useLoaderData<LoaderData>();
  const [searchParams] = useSearchParams();
  const submit = useSubmit();
  const fetcher = useFetcher();

  useEffect(() => {
    if (needsSeeding && fetcher.state === 'idle') {
      console.log("Task templates need seeding. Initiating seed...");
      fetcher.submit({ _action: "seedTaskTemplates" }, { method: "post" });
    }
  }, [needsSeeding, fetcher, tasks.length]);

  const queryValue = searchParams.get("query") || "";
  const selectedCategory = searchParams.get("category") || "all";

  // Use bacterial utility to create tab state
  const tabState = useMemo(() =>
    createTabState(LIBRARY_TABS, selectedCategory),
    [selectedCategory]
  );

  // Use bacterial utility to create tab change handler
  const handleTabChange = createTabChangeHandler(LIBRARY_TABS, (newCategory) => {
    const formData = createSearchFormData({
      query: queryValue,
      category: newCategory,
      page: 1
    });
    submit(formData, { method: 'get', replace: true });
  });

  const handlePageChange = (newPage: number) => {
    const formData = createSearchFormData({
      query: queryValue,
      category: selectedCategory,
      page: newPage
    });
    submit(formData, { method: 'get', replace: true });
  }

  // Use bacterial utility to create automation lookup set
  const installedSet = createAutomationLookupSet(installedTypes.map(type => ({ type })));

  // Display a message while seeding or if seeding failed and tasks are empty
  if (fetcher.state !== 'idle' && tasks.length === 0) {
    return (
        <Page title="Task Library">
            <Layout>
                <Layout.Section>
                    <Card>
                        <Text variant="bodyMd" as="p">Seeding task templates... Please wait.</Text>
                    </Card>
                </Layout.Section>
            </Layout>
        </Page>
    );
  }

  return (
    <Page title="Task Library">
      <Layout>
        <Layout.Section>
          <Form method="get">
            <input type="hidden" name="category" value={selectedCategory} />
            <TextField
              label="Search Tasks"
              labelHidden
              name="query"
              value={queryValue}
              onChange={(value) => {
                // Use bacterial utility to create search form data
                const formData = createSearchFormData({
                  query: value,
                  category: selectedCategory,
                  page: 1
                });
                submit(formData, { method: "get", replace: true });
              }}
              placeholder="Search for automation tasks..."
              autoComplete="off"
            />
          </Form>
        </Layout.Section>
        <Layout.Section>
          <Tabs
            tabs={LIBRARY_TABS}
            selected={tabState.selectedIndex}
            onSelect={handleTabChange}
          />
        </Layout.Section>
        <Layout.Section>
          <Grid>
            {tasks.map(task => {
              // Use bacterial utility to check if task is installed
              const taskInstalled = isTaskInstalled(task.type, installedSet);
              return (
              <Grid.Cell key={task.id} columnSpan={{xs: 6, sm: 3, md: 3, lg: 4, xl: 4}}>
                <Card>
                    <BlockStack gap="300">
                        <Text variant="headingMd" as="h2">{task.title}</Text>
                        <Text variant="bodyMd" as="p" tone="subdued">{task.description}</Text>
                        <RemixLink
                          to={taskInstalled ? "/app/automations" : `/app/automations/configure/${task.id}`}
                          style={{ textDecoration: 'none', display: 'block' }}
                        >
                            <Button
                                variant={taskInstalled ? "secondary" : "primary"}
                                fullWidth
                            >
                                {taskInstalled ? "Manage" : "Configure & Add"}
                            </Button>
                        </RemixLink>
                    </BlockStack>
                </Card>
              </Grid.Cell>
            )})}
          </Grid>
          {tasks.length === 0 && !needsSeeding && fetcher.state === 'idle' && (
            <Card>
                <Text variant="bodyMd" as="p">
                    No tasks found matching your criteria.
                </Text>
            </Card>
          )}
        </Layout.Section>
        {tasks.length > 0 && totalPages > 1 && (
          <Layout.Section>
            <InlineStack align="center">
              <NumberedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </InlineStack>
          </Layout.Section>
        )}
      </Layout>
      {/* Add some spacing at the bottom of the page */}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}